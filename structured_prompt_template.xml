<?xml version="1.0" encoding="UTF-8"?>
<prompt_template>
    <metadata>
        <title>结构化提示模板</title>
        <version>1.0</version>
        <description>包含六个核心组件的标准化提示结构</description>
    </metadata>
    
    <components>
        <background>
            <description>背景信息和情景细节</description>
            <content>
                <!-- 在此处填写相关背景信息 -->
                <!-- 包括：项目背景、业务场景、技术环境、现有条件等 -->
            </content>
        </background>
        
        <objective>
            <description>明确的目标和预期成果</description>
            <content>
                <!-- 在此处定义具体目标 -->
                <!-- 包括：主要目标、次要目标、成功标准、可衡量的结果等 -->
            </content>
        </objective>
        
        <style>
            <description>写作方法和格式偏好</description>
            <content>
                <!-- 在此处指定写作风格 -->
                <!-- 包括：结构化程度、详细程度、技术深度、表达方式等 -->
            </content>
        </style>
        
        <tone>
            <description>沟通风格和情感方式</description>
            <content>
                <!-- 在此处定义语气特征 -->
                <!-- 包括：正式/非正式、友好/严肃、鼓励/中性、专业程度等 -->
            </content>
        </tone>
        
        <audience>
            <description>目标用户及其特征</description>
            <content>
                <!-- 在此处描述目标受众 -->
                <!-- 包括：技术水平、职业角色、经验背景、需求特点等 -->
            </content>
        </audience>
        
        <response>
            <description>预期输出格式和结构</description>
            <content>
                <!-- 在此处指定输出要求 -->
                <!-- 包括：格式类型、结构组织、长度要求、包含元素等 -->
            </content>
        </response>
    </components>
    
    <usage_instructions>
        <step number="1">根据具体需求填写每个组件的内容</step>
        <step number="2">确保各组件之间保持一致性和连贯性</step>
        <step number="3">根据实际情况调整组件的详细程度</step>
        <step number="4">验证模板是否能够产生预期的输出结果</step>
    </usage_instructions>
</prompt_template>
