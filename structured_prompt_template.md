# 结构化提示模板

## 模板结构

```
<<这是你的角色>>{your_role}<</这是你的角色>>

<<你的任务>>{your_task}<</你的任务>>

<<要求>>{specification}<</要求>>

<<输出格式>>{response_format}<</输出格式>>
```

## 六个核心组件详解

### 1. 背景 (Background)
- **用途**: 提供背景信息和情景细节
- **包含内容**: 项目背景、业务场景、技术环境、现有条件
- **在模板中的位置**: 通常融入到角色定义和任务描述中

### 2. 目标 (Objective) 
- **用途**: 明确的目标和预期成果
- **包含内容**: 主要目标、次要目标、成功标准、可衡量的结果
- **在模板中的位置**: 主要体现在"你的任务"部分

### 3. 风格 (Style)
- **用途**: 写作方法和格式偏好  
- **包含内容**: 结构化程度、详细程度、技术深度、表达方式
- **在模板中的位置**: 体现在"要求"和"输出格式"中

### 4. 语气 (Tone)
- **用途**: 沟通风格和情感方式
- **包含内容**: 正式/非正式、友好/严肃、鼓励/中性、专业程度
- **在模板中的位置**: 融入到角色定义和要求中

### 5. 受众 (Audience)
- **用途**: 目标用户及其特征
- **包含内容**: 技术水平、职业角色、经验背景、需求特点
- **在模板中的位置**: 体现在角色定义和输出格式要求中

### 6. 响应 (Response)
- **用途**: 预期输出格式和结构
- **包含内容**: 格式类型、结构组织、长度要求、包含元素
- **在模板中的位置**: 主要在"输出格式"部分

## 使用示例

### 示例1: 技术产品专家数据库设计

```
<<这是你的角色>>
你是一位资深的技术产品专家和数据库架构师，拥有10年以上的SaaS产品开发经验。
你擅长分析复杂的业务场景，识别核心实体和关系，设计高效、可扩展的数据库架构。
你的专业领域包括多租户架构、权限管理、性能优化和数据建模最佳实践。
<</这是你的角色>>

<<你的任务>>
分析项目管理平台的业务场景，设计完整的数据库表模式。
该平台需要支持多租户架构，包含用户管理、项目管理、任务分配、团队协作、
文件管理、评论系统和通知功能。请识别核心实体、定义表结构、
建立关系模型，并提供性能优化建议。
<</你的任务>>

<<要求>>
1. 采用系统性分析方法，先识别业务实体再设计表结构
2. 使用标准的数据库设计最佳实践
3. 考虑数据完整性、查询性能和扩展性
4. 提供具体的字段定义和数据类型
5. 包含索引策略和约束建议
6. 语言风格专业严谨，使用清晰的技术术语
<</要求>>

<<输出格式>>
请按以下结构输出：
1. **业务分析** - 核心实体识别和关系梳理
2. **表结构设计** - 详细的表定义和字段说明  
3. **关系模型** - ER图描述或关系说明
4. **索引策略** - 性能优化建议
5. **SQL示例** - 关键表的DDL语句
6. **扩展性考虑** - 未来发展的设计预留

每部分要求结构清晰，重点突出，包含实用的实现指导。
<</输出格式>>
```

## 模板使用指南

1. **填写角色**: 明确专业身份、经验背景、核心能力
2. **定义任务**: 具体、可执行的工作目标和范围
3. **制定要求**: 质量标准、方法论、风格偏好
4. **规范格式**: 输出结构、长度要求、包含元素

## 优势特点

- **简洁明了**: 避免复杂的XML结构
- **灵活适用**: 可根据不同场景调整内容
- **结构清晰**: 四个核心部分覆盖六个组件
- **易于使用**: 直接复制粘贴即可使用
